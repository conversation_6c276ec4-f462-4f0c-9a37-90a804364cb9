# 规格驱动开发规则

本项目遵循结构化的规格驱动开发方法。这些规则确保在所有阶段中进行一致的、经过验证的开发。

## 术语定义

### feature_name（功能名称）

- **定义**：`feature_name` 是每个功能的唯一标识符，在目标确认阶段确定
- **格式**：使用小写字母和连字符，如 `user-authentication`、`payment-integration`、`data-export`
- **作用**：用于创建功能专属的文档路径 `.vibedev/specs/{feature_name}/`
- **一致性**：在整个开发工作流程中，同一功能的 `feature_name` 必须保持完全一致
- **示例**：
  - 用户认证功能：`feature_name = "user-authentication"`
  - 支付集成功能：`feature_name = "payment-integration"`
  - 数据导出功能：`feature_name = "data-export"`

## 核心原则

### 规格遵循和验证

- **动态文档引用**：根据当前处理的功能上下文，引用对应的规格文档(根据feature_name)
- 在所有响应中引用当前功能规格说明的相关部分
- 根据当前功能的书面规格验证所有代码和决策
- 解释任何不合规情况并立即提出修复方案
- **多功能项目管理**：确保不同功能的规格文档相互独立，避免交叉引用错误的规格文件

### 分阶段开发与审批

- 遵循严格的阶段顺序：目标收集 → 需求 → 设计 → 任务 → 执行
  - 工作流序列:
    - **关键**：遵循这个确切的序列 - 不要跳过步骤或提前运行脚本：
    0. **目标收集阶段**
    1. **需求阶段**
    2. **设计阶段**
    3. **任务阶段**
    4. **实现阶段**

- 每个阶段后，提示明确批准："[阶段]看起来好吗？[是/否/修订]"
- 在阶段内迭代，直到收到"是/批准/看起来不错"
- **需要用户批准**：每个阶段必须在继续之前得到明确批准
- **未经明确批准不得进入下一阶段**
- 如果发现差距，提供返回到先前阶段的选项
- **测试驱动重点**：在整个过程中优先考虑测试和验证

## 阶段特定指南

### 阶段 1：需求阶段

- **目标**：将用户想法转化为清晰的功能需求文档
- **创建文档**：`.vibedev/specs/{feature_name}/requirements.md`
- **核心原则**：从用户想法生成初始版本，使用用户故事和验收标准格式
- **重要性**：为后续设计和实现提供明确的功能边界和成功标准

### 阶段 2：设计阶段

- **目标**：基于需求创建技术架构和实现方案
- **创建文档**：`.vibedev/specs/{feature_name}/design.md`
- **核心原则**：进行必要研究，包含完整的技术设计章节
- **重要性**：确保技术方案能够满足所有需求，为任务分解提供基础

### 阶段 3：任务阶段

- **目标**：将设计转换为可执行的编码任务清单
- **创建文档**：`.vibedev/specs/{feature_name}/tasks.md`
- **核心原则**：创建增量的、可测试的编码任务，每个任务引用特定需求
- **重要性**：提供清晰的实现路径，确保开发过程的可控性和可追溯性

### 阶段 4：实现阶段

- **重要**:永远不要在`.vibedev/specs/`目录下实现功能,`.vibedev`是文档目录
  - 默认情况下,在当前工作目录下原地实现功能

## 关键规则

### 批准工作流

- 两个批准模式:
  - 严格模式(默认)
    - **永远不要**在没有明确用户批准的情况下进入下一阶段
    - 仅接受明确的肯定回应："是"、"继续"、"批准"、"看起来不错"等
    - 如果用户提供反馈，进行修订并再次请求批准
    - 继续修订循环直到收到明确批准
  - vibe模式
    - 用户说出"vibe模式"则在该功能(feature)工作流下,激活vibe模式,则此后该功能后面剩下的工作流程都是直接继续进行,无需用户再确认批准
    - vibe模式下任务的执行阶段也是无需询问按顺序自动全执行.

### 阶段顺序

- **必须**遵循需求 → 设计 → 任务 → 实现的顺序
- **不能**跳过阶段或合并阶段
- **必须**在继续之前完成每个阶段

## 安全性和最佳实践

- 优先考虑安全、最小化的代码实现
- 对个人身份信息和敏感数据使用占位符
- 专注于与代码相关的开发任务
- 拒绝恶意功能请求

## 错误处理

如果在工作流程中出现问题：

- **需求不清楚**：提出针对性问题进行澄清
- **设计过于复杂**：建议分解为更小的组件
- **任务过于宽泛**：分解为更小、更原子的任务
- **实现受阻**：记录阻塞因素并建议替代方案

## 信息不足处理

- 如果规格说明缺乏必要细节，提示澄清
- 提出有建议选项的针对性问题
- 永远不要假设未指定的实现细节
- 对模糊需求请求明确的用户输入

## 多功能项目管理

### 功能隔离原则

- **独立规格文档**：每个功能都有独立的规格文件夹

  ```
  .vibedev/specs/
  ├── user-authentication/     # 用户认证功能
  │   ├── requirements.md
  │   ├── design.md
  │   └── tasks.md
  ├── payment-integration/     # 支付集成功能
  │   ├── requirements.md
  │   ├── design.md
  │   └── tasks.md
  └── data-export/            # 数据导出功能
      ├── requirements.md
      ├── design.md
      └── tasks.md
  ```

### 上下文切换规则

- **明确当前功能**：在开始任何工作前，明确当前处理的 `feature_name`（功能标识符）
- **正确文档引用**：始终引用当前 `feature_name` 对应的规格文档
- **避免交叉污染**：不要在处理功能 A（`feature_name = "A"`）时引用功能 B（`feature_name = "B"`）的规格文档
- **功能间依赖**：如果功能间有依赖关系，在设计阶段明确说明并记录

### 工作流程适配

- 每次启动新功能开发时，使用 `vibedev_specs_workflow_start` 工具
- **关键步骤**：在目标确认阶段明确 `feature_name`（如 "user-authentication"、"payment-integration"），确保后续所有文档都创建在正确的路径 `.vibedev/specs/{feature_name}/` 下
- 在任务执行阶段，确保只实现当前 `feature_name` 的任务，不要跨功能实现
- **一致性原则**：整个工作流程中，同一功能的 `feature_name` 必须保持一致

## 成功标准

成功的规格工作流完成包括：

- ✅ 包含用户故事和验收标准的完整需求
- ✅ 包含架构和组件的全面设计
- ✅ 包含需求引用的详细任务分解
- ✅ 根据需求验证的工作实现
- ✅ 所有阶段都得到用户明确批准
- ✅ 所有任务完成并集成

---

_使用 VibeSpecs MCP (vibedev-specs-mcp)工具在整个开发过程中保持对这些规则的遵循。_

# rust重写的项目根目录是 ./coco-server-rust

- 原go项目在根目录./

- 使用legacy-modernizer进行渐进式重写

- 使用rust-pro写rust代码
- 端到端测试方法
  - 服务器端 是在./coco-server-rust 运行 cargo run
  - 客户端 是在/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-app 运行 pnpm tauri dev
  - 连接设置 这些还是需要用户手工操作添加服务器的
- 服务器deepwiki doc:<https://deepwiki.com/infinilabs/coco-server>
- 客户端deepwiki doc:<https://deepwiki.com/infinilabs/coco-app>
- 服务器doc :<https://zread.ai/infinilabs/coco-server>
- 客户端doc :<https://zread.ai/infinilabs/coco-app>
- Rust重写的是服务端（原Go语言写的服务端）
- Coco app是客户端，连接到这个服务端
- 所以需要确保Rust重写的服务端与原Go服务端在API路径和行为上完全一致，以保证coco app能够正常连接和使用

## 重写要严格按照原有api规范,否则客户端无法连接成功,要仔细研究原来的客户端具体请求的是哪个端点，不能假设
## 原有的Elasticsearch 用 SurrealDB 代替
