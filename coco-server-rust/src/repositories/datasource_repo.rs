use crate::error::error::CocoError;
use crate::models::datasource::*;
use crate::repositories::cache_manager::{cache_keys, CacheManager};
use crate::repositories::elasticsearch::ElasticsearchClient;
use chrono::Utc;
use elasticsearch::{DeleteParts, GetParts, IndexParts, SearchParts, UpdateParts};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// DataSource仓库错误类型
#[derive(Debug, thiserror::Error)]
pub enum RepositoryError {
    #[error("数据源不存在: {0}")]
    NotFound(String),

    #[error("数据库操作失败: {0}")]
    DatabaseError(String),

    #[error("缓存操作失败: {0}")]
    CacheError(String),

    #[error("序列化错误: {0}")]
    SerializationError(String),

    #[error("验证错误: {0}")]
    ValidationError(String),
}

/// DataSource数据访问仓库
///
/// 提供DataSource的CRUD操作，集成Elasticsearch和缓存
pub struct DataSourceRepository {
    /// Elasticsearch客户端
    elasticsearch: Arc<ElasticsearchClient>,
    /// 缓存管理器
    cache_manager: Arc<CacheManager>,
}

impl DataSourceRepository {
    /// 创建新的DataSource仓库实例
    ///
    /// # 参数
    /// * `elasticsearch` - Elasticsearch客户端
    /// * `cache_manager` - 缓存管理器
    pub fn new(elasticsearch: Arc<ElasticsearchClient>, cache_manager: Arc<CacheManager>) -> Self {
        Self {
            elasticsearch,
            cache_manager,
        }
    }

    /// 创建新的数据源
    ///
    /// # 参数
    /// * `datasource` - 数据源对象
    ///
    /// # 返回
    /// 创建成功返回数据源ID
    pub async fn create(&self, datasource: &DataSource) -> Result<String, RepositoryError> {
        // 生成唯一ID
        let id = Uuid::new_v4().to_string().replace('-', "");

        // 设置时间戳
        let mut ds = datasource.clone();
        let now = Utc::now();
        ds.id = Some(id.clone());
        ds.created = Some(now);
        ds.updated = Some(now);

        // 序列化数据源
        let doc_body = serde_json::to_value(&ds)
            .map_err(|e| RepositoryError::SerializationError(e.to_string()))?;

        // 存储到Elasticsearch
        let response = self
            .elasticsearch
            .client()
            .index(IndexParts::IndexId(self.elasticsearch.default_index(), &id))
            .body(doc_body)
            .send()
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        if !response.status_code().is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            error!("创建数据源失败: {}", error_text);
            return Err(RepositoryError::DatabaseError(error_text));
        }

        // 更新缓存
        self.set_cache(&id, &ds).await?;
        self.invalidate_list_caches().await?;

        info!("数据源创建成功: id={}", id);
        Ok(id)
    }

    /// 根据ID获取数据源
    ///
    /// # 参数
    /// * `id` - 数据源ID
    ///
    /// # 返回
    /// 如果找到返回Some(DataSource)，否则返回None
    pub async fn get_by_id(&self, id: &str) -> Result<Option<DataSource>, RepositoryError> {
        // 先尝试从缓存获取
        if let Some(cached) = self.get_from_cache(id).await? {
            debug!("从缓存获取数据源: id={}", id);
            return Ok(Some(cached));
        }

        // 从Elasticsearch获取
        let response = self
            .elasticsearch
            .client()
            .get(GetParts::IndexId(self.elasticsearch.default_index(), id))
            .send()
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        if response.status_code().as_u16() == 404 {
            debug!("数据源不存在: id={}", id);
            return Ok(None);
        }

        if !response.status_code().is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            error!("获取数据源失败: {}", error_text);
            return Err(RepositoryError::DatabaseError(error_text));
        }

        // 解析响应
        let response_body: Value = response
            .json()
            .await
            .map_err(|e| RepositoryError::SerializationError(e.to_string()))?;

        if let Some(source) = response_body.get("_source") {
            let datasource: DataSource = serde_json::from_value(source.clone())
                .map_err(|e| RepositoryError::SerializationError(e.to_string()))?;

            // 更新缓存
            self.set_cache(id, &datasource).await?;

            debug!("从数据库获取数据源: id={}", id);
            Ok(Some(datasource))
        } else {
            Ok(None)
        }
    }

    /// 更新数据源
    ///
    /// # 参数
    /// * `id` - 数据源ID
    /// * `datasource` - 更新的数据源对象
    pub async fn update(&self, id: &str, datasource: &DataSource) -> Result<(), RepositoryError> {
        // 设置更新时间
        let mut ds = datasource.clone();
        ds.updated = Some(Utc::now());

        // 序列化数据源
        let doc_body = serde_json::to_value(&ds)
            .map_err(|e| RepositoryError::SerializationError(e.to_string()))?;

        // 更新Elasticsearch
        let response = self
            .elasticsearch
            .client()
            .update(UpdateParts::IndexId(self.elasticsearch.default_index(), id))
            .body(json!({
                "doc": doc_body
            }))
            .send()
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        if response.status_code().as_u16() == 404 {
            return Err(RepositoryError::NotFound(id.to_string()));
        }

        if !response.status_code().is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            error!("更新数据源失败: {}", error_text);
            return Err(RepositoryError::DatabaseError(error_text));
        }

        // 更新缓存
        self.set_cache(id, &ds).await?;
        self.invalidate_list_caches().await?;

        info!("数据源更新成功: id={}", id);
        Ok(())
    }

    /// 删除数据源
    ///
    /// # 参数
    /// * `id` - 数据源ID
    pub async fn delete(&self, id: &str) -> Result<(), RepositoryError> {
        // 从Elasticsearch删除
        let response = self
            .elasticsearch
            .client()
            .delete(DeleteParts::IndexId(self.elasticsearch.default_index(), id))
            .send()
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        if response.status_code().as_u16() == 404 {
            return Err(RepositoryError::NotFound(id.to_string()));
        }

        if !response.status_code().is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            error!("删除数据源失败: {}", error_text);
            return Err(RepositoryError::DatabaseError(error_text));
        }

        // 清理缓存
        self.invalidate_cache(id).await?;
        self.invalidate_list_caches().await?;

        info!("数据源删除成功: id={}", id);
        Ok(())
    }

    /// 搜索数据源
    ///
    /// # 参数
    /// * `query` - 搜索查询参数
    ///
    /// # 返回
    /// 搜索结果
    pub async fn search(&self, query: &SearchQuery) -> Result<SearchResponse, RepositoryError> {
        // 构建Elasticsearch查询
        let mut search_body = json!({
            "query": {
                "match_all": {}
            }
        });

        // 处理查询字符串
        if let Some(q) = &query.q {
            if !q.is_empty() {
                search_body["query"] = json!({
                    "multi_match": {
                        "query": q,
                        "fields": ["name^2", "description", "category", "combined_fulltext"]
                    }
                });
            }
        }

        // 处理分页
        if let Some(size) = query.size {
            search_body["size"] = json!(size);
        }
        if let Some(from) = query.from {
            search_body["from"] = json!(from);
        }

        // 处理排序
        if let Some(sort) = &query.sort {
            search_body["sort"] = json!([{sort: {"order": "asc"}}]);
        }

        debug!("执行搜索查询: {}", search_body);

        // 执行搜索
        let response = self
            .elasticsearch
            .client()
            .search(SearchParts::Index(&[self.elasticsearch.default_index()]))
            .body(search_body)
            .send()
            .await
            .map_err(|e| RepositoryError::DatabaseError(e.to_string()))?;

        if !response.status_code().is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            error!("搜索数据源失败: {}", error_text);
            return Err(RepositoryError::DatabaseError(error_text));
        }

        // 解析搜索结果
        let search_result: Value = response
            .json()
            .await
            .map_err(|e| RepositoryError::SerializationError(e.to_string()))?;

        self.parse_search_response(search_result)
    }

    /// 解析Elasticsearch搜索响应
    fn parse_search_response(&self, response: Value) -> Result<SearchResponse, RepositoryError> {
        let took = response["took"].as_u64().unwrap_or(0);
        let timed_out = response["timed_out"].as_bool().unwrap_or(false);

        let hits_obj = &response["hits"];
        let total_value = hits_obj["total"]["value"].as_u64().unwrap_or(0);
        let total_relation = hits_obj["total"]["relation"]
            .as_str()
            .unwrap_or("eq")
            .to_string();
        let max_score = hits_obj["max_score"].as_f64();

        let mut hits = Vec::new();
        if let Some(hits_array) = hits_obj["hits"].as_array() {
            for hit in hits_array {
                if let Ok(search_hit) = self.parse_search_hit(hit) {
                    hits.push(search_hit);
                }
            }
        }

        Ok(SearchResponse {
            took,
            timed_out,
            hits: SearchHits {
                total: SearchTotal {
                    value: total_value,
                    relation: total_relation,
                },
                max_score,
                hits,
            },
        })
    }

    /// 解析单个搜索结果
    fn parse_search_hit(&self, hit: &Value) -> Result<SearchHit, RepositoryError> {
        let index = hit["_index"].as_str().unwrap_or("").to_string();
        let doc_type = hit["_type"].as_str().unwrap_or("_doc").to_string();
        let id = hit["_id"].as_str().unwrap_or("").to_string();
        let score = hit["_score"].as_f64();

        let source: DataSource = serde_json::from_value(hit["_source"].clone())
            .map_err(|e| RepositoryError::SerializationError(e.to_string()))?;

        Ok(SearchHit {
            index,
            doc_type,
            id,
            score,
            source,
        })
    }

    /// 从缓存获取数据源
    ///
    /// # 参数
    /// * `id` - 数据源ID
    pub async fn get_from_cache(&self, id: &str) -> Result<Option<DataSource>, RepositoryError> {
        let cache_key = cache_keys::build_key(cache_keys::DATASOURCE_PRIMARY_PREFIX, id);

        if let Some(cached_json) = self.cache_manager.get(&cache_key) {
            match serde_json::from_str::<DataSource>(&cached_json) {
                Ok(datasource) => {
                    debug!("从缓存获取数据源成功: id={}", id);
                    Ok(Some(datasource))
                }
                Err(e) => {
                    warn!("缓存数据反序列化失败: {}, 删除缓存项", e);
                    self.cache_manager.remove(&cache_key);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }

    /// 设置数据源缓存
    ///
    /// # 参数
    /// * `id` - 数据源ID
    /// * `datasource` - 数据源对象
    pub async fn set_cache(
        &self,
        id: &str,
        datasource: &DataSource,
    ) -> Result<(), RepositoryError> {
        let cache_key = cache_keys::build_key(cache_keys::DATASOURCE_PRIMARY_PREFIX, id);

        match serde_json::to_string(datasource) {
            Ok(json_str) => {
                self.cache_manager
                    .set(cache_key, json_str, None)
                    .map_err(|e| RepositoryError::CacheError(e))?;
                debug!("数据源缓存设置成功: id={}", id);
                Ok(())
            }
            Err(e) => {
                error!("数据源序列化失败: {}", e);
                Err(RepositoryError::SerializationError(e.to_string()))
            }
        }
    }

    /// 失效数据源缓存
    ///
    /// # 参数
    /// * `id` - 数据源ID
    pub async fn invalidate_cache(&self, id: &str) -> Result<(), RepositoryError> {
        let cache_key = cache_keys::build_key(cache_keys::DATASOURCE_PRIMARY_PREFIX, id);
        self.cache_manager.remove(&cache_key);
        debug!("数据源缓存已失效: id={}", id);
        Ok(())
    }

    /// 失效列表相关缓存
    pub async fn invalidate_list_caches(&self) -> Result<(), RepositoryError> {
        self.cache_manager
            .remove(cache_keys::ENABLED_DATASOURCE_IDS);
        self.cache_manager
            .remove(cache_keys::DISABLED_DATASOURCE_IDS);
        debug!("列表缓存已失效");
        Ok(())
    }
}

/// 错误类型转换
impl From<RepositoryError> for CocoError {
    fn from(error: RepositoryError) -> Self {
        match error {
            RepositoryError::NotFound(msg) => {
                CocoError::InvalidRequest(format!("资源不存在: {}", msg))
            }
            RepositoryError::DatabaseError(msg) => {
                CocoError::ServerError(format!("数据库错误: {}", msg))
            }
            RepositoryError::CacheError(msg) => {
                CocoError::ServerError(format!("缓存错误: {}", msg))
            }
            RepositoryError::SerializationError(msg) => {
                CocoError::ServerError(format!("序列化错误: {}", msg))
            }
            RepositoryError::ValidationError(msg) => {
                CocoError::InvalidRequest(format!("验证错误: {}", msg))
            }
        }
    }
}
